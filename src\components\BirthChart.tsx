'use client';

import { useState, useEffect } from 'react';
import { MapPin, Calendar, Clock, ChevronDown, ChevronUp, Loader2, AlertCircle } from 'lucide-react';
import VedicChart from './VedicChart';
import AstrologicalTables from './AstrologicalTables';
import TranslatedText from './TranslatedText';
import { useUITranslation } from '@/utils/ui-translations';
import {
  calculateEnhancedBirthChart,
  BirthChartData,
  BirthDetails,
  updateChartSymbols
} from '@/lib/astrology';

interface BirthChartProps {
  birthChart: {
    id: string;
    ascendant: string;
    moonSign: string;
    sunSign: string;
    planetPositions: any[];
    housePositions: any[];
    aspects: any[];
    nakshatras: any[];
    dashas: any[];
    // Enhanced Vedic Chart Data
    lagnaChart?: any;
    navamsaChart?: any;
    chandraChart?: any;
    karakTable?: any;
    avasthaTable?: any;
    planetaryDetails?: any;
    vimshottariDasha?: any;
    ashtakavarga?: any;
    // Reading content
    generalReading?: string;
    strengthsWeaknesses?: string;
    careerGuidance?: string;
    relationshipGuidance?: string;
    healthGuidance?: string;
    readingsEn?: any;
    readingsSi?: any;
    calculatedAt: string;
    user?: {
      name: string;
      birthDate: string;
      birthTime?: string;
      birthPlace?: string;
    };
  };
  className?: string;
  loading?: boolean;
  error?: string | null;
}

export default function BirthChart({ birthChart, className = '', loading = false, error = null }: BirthChartProps) {
  const [showPlanetDetails, setShowPlanetDetails] = useState(false);
  const [activeTab, setActiveTab] = useState<'charts' | 'tables' | 'readings'>('charts');
  const { t } = useUITranslation();

  // Loading state
  if (loading) {
    return (
      <div className={`bg-gradient-to-br from-purple-900/40 to-blue-900/40 backdrop-blur-sm rounded-2xl border border-purple-500/20 shadow-2xl ${className}`}>
        <div className="p-8 text-center">
          <Loader2 className="mx-auto mb-4 text-purple-400 animate-spin" size={48} />
          <h3 className="text-xl font-semibold text-white mb-2">{t('loading_birth_chart')}</h3>
          <p className="text-purple-200">{t('calculating_cosmic_blueprint')}</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={`bg-gradient-to-br from-red-900/40 to-orange-900/40 backdrop-blur-sm rounded-2xl border border-red-500/20 shadow-2xl ${className}`}>
        <div className="p-8 text-center">
          <AlertCircle className="mx-auto mb-4 text-red-400" size={48} />
          <h3 className="text-xl font-semibold text-white mb-2">{t('error_loading_birth_chart')}</h3>
          <p className="text-red-200 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg transition-colors"
          >
            {t('try_again')}
          </button>
        </div>
      </div>
    );
  }

  // Use the enhanced chart data from the birth chart if available
  const enhancedChart: BirthChartData = {
    ascendant: birthChart.ascendant,
    moonSign: birthChart.moonSign,
    sunSign: birthChart.sunSign,
    planets: birthChart.planetPositions || [],
    houses: birthChart.housePositions || [],
    aspects: birthChart.aspects || [],
    nakshatras: birthChart.nakshatras || [],
    dashas: birthChart.dashas || [],
    lagnaChart: birthChart.lagnaChart ? updateChartSymbols(birthChart.lagnaChart, t) : undefined,
    navamsaChart: birthChart.navamsaChart ? updateChartSymbols(birthChart.navamsaChart, t) : undefined,
    chandraChart: birthChart.chandraChart ? updateChartSymbols(birthChart.chandraChart, t) : undefined,
    karakTable: birthChart.karakTable,
    avasthaTable: birthChart.avasthaTable,
    planetaryDetails: birthChart.planetaryDetails,
    vimshottariDasha: birthChart.vimshottariDasha,
    ashtakavarga: birthChart.ashtakavarga
  };

  // Check if we have enhanced chart data
  const hasEnhancedData = enhancedChart.lagnaChart || enhancedChart.navamsaChart || enhancedChart.chandraChart;

  // Check if we have basic chart data
  const hasBasicData = birthChart.planetPositions && birthChart.planetPositions.length > 0;

  // Debug logging
  console.log('🔍 BirthChart component - Enhanced data check:', {
    hasEnhancedData,
    hasBasicData,
    lagnaChart: !!enhancedChart.lagnaChart,
    navamsaChart: !!enhancedChart.navamsaChart,
    chandraChart: !!enhancedChart.chandraChart,
    planetaryDetails: !!enhancedChart.planetaryDetails,
    karakTable: !!enhancedChart.karakTable,
    avasthaTable: !!enhancedChart.avasthaTable,
    vimshottariDasha: !!enhancedChart.vimshottariDasha,
    ashtakavarga: !!enhancedChart.ashtakavarga
  });

  const renderCharts = () => {
    try {
      if (!hasEnhancedData) {
        return (
          <div className="text-center py-12">
            <div className="bg-purple-800/20 rounded-lg p-6 border border-purple-500/20">
              <h3 className="text-lg font-semibold text-white mb-2">{t('enhanced_vedic_charts')}</h3>
              <p className="text-purple-200 mb-4">
                {t('enhanced_vedic_charts_not_available')}
              </p>
              <p className="text-sm text-purple-300 mb-4">
                {t('basic_system_generated')}
              </p>
            <div className="text-xs text-purple-400 bg-purple-900/30 rounded p-3">
              <p className="mb-1">{t('debug_info')}:</p>
              <p>{t('lagna_chart_available')}: {enhancedChart.lagnaChart ? `✅ ${t('available')}` : `❌ ${t('missing')}`}</p>
              <p>{t('navamsa_chart_available')}: {enhancedChart.navamsaChart ? `✅ ${t('available')}` : `❌ ${t('missing')}`}</p>
              <p>{t('chandra_chart_available')}: {enhancedChart.chandraChart ? `✅ ${t('available')}` : `❌ ${t('missing')}`}</p>
              <p>{t('planet_positions_available')}: {hasBasicData ? `✅ ${t('available')}` : `❌ ${t('missing')}`}</p>
            </div>
            </div>
          </div>
        );
      }

      console.log('🎨 Rendering enhanced charts');
      console.log('🔍 Enhanced chart data:', {
        lagnaChart: !!enhancedChart.lagnaChart,
        navamsaChart: !!enhancedChart.navamsaChart,
        chandraChart: !!enhancedChart.chandraChart
      });

      return (
        <div className="w-full space-y-8 md:space-y-12" style={{ display: 'block', visibility: 'visible' }}>
          {/* Header Section */}
          <div className="text-center mb-6 md:mb-8">
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-purple-300 via-blue-300 to-purple-300 bg-clip-text text-transparent mb-4">
              ⭐ {t('birth_chart')} ({t('handahana')}) ⭐
            </h2>
            <p className="text-gray-300 text-base md:text-lg max-w-3xl mx-auto">
              <TranslatedText text={t('personalized_vedic_charts_description')} />
            </p>
          </div>

          {/* Main Charts Grid - Simplified Layout */}
          <div className="w-full space-y-8 lg:space-y-0 lg:grid lg:grid-cols-2 lg:gap-12"
               style={{ display: 'block !important', visibility: 'visible !important' }}>
            {/* Lagna Chart (D1) */}
            {enhancedChart.lagnaChart && (
              <div className="w-full"
                   style={{ display: 'block !important', visibility: 'visible !important' }}>
                <VedicChart
                  chartData={enhancedChart.lagnaChart}
                  title={`✨ ${t('lagna_chart')} (D1) ✨`}
                  className=""
                />
              </div>
            )}

            {/* Navamsa Chart (D9) */}
            {enhancedChart.navamsaChart && (
              <div className="w-full"
                   style={{ display: 'block !important', visibility: 'visible !important' }}>
                <VedicChart
                  chartData={enhancedChart.navamsaChart}
                  title={`✨ ${t('navamsa_chart')} (D9) ✨`}
                  className=""
                />
              </div>
            )}
          </div>

          {/* Chandra Chart - Centered */}
          {enhancedChart.chandraChart && (
            <div className="w-full mt-8 lg:mt-12"
                 style={{ display: 'block !important', visibility: 'visible !important' }}>
              <VedicChart
                chartData={enhancedChart.chandraChart}
                title={`✨ ${t('chandra_chart')} ✨`}
                className=""
              />
            </div>
          )}
        </div>
      );
    } catch (error) {
      console.error('Error rendering charts:', error);
      return (
        <div className="text-center py-12">
          <div className="bg-red-800/20 rounded-lg p-6 border border-red-500/20">
            <AlertCircle className="mx-auto mb-4 text-red-400" size={48} />
            <h3 className="text-lg font-semibold text-white mb-2">{t('error_loading_charts')}</h3>
            <p className="text-red-200">{t('error_loading_charts_message')}</p>
          </div>
        </div>
      );
    }
  };

  const renderTables = () => {
    try {
      if (!hasEnhancedData) {
        return (
          <div className="text-center py-12">
            <div className="bg-purple-800/20 rounded-lg p-6 border border-purple-500/20">
              <h3 className="text-lg font-semibold text-white mb-2">{t('astrological_data_tables')}</h3>
              <p className="text-purple-200 mb-4">
                {t('astrological_tables_not_available')}
              </p>
              <p className="text-sm text-purple-300 mb-4">
                {t('enhanced_tables_missing')}
              </p>
            <div className="text-xs text-purple-400 bg-purple-900/30 rounded p-3">
              <p className="mb-1">{t('debug_info')}:</p>
              <p>{t('karaka_table_available')}: {enhancedChart.karakTable ? `✅ ${t('available')}` : `❌ ${t('missing')}`}</p>
              <p>{t('avastha_table_available')}: {enhancedChart.avasthaTable ? `✅ ${t('available')}` : `❌ ${t('missing')}`}</p>
              <p>{t('planetary_details_available')}: {enhancedChart.planetaryDetails ? `✅ ${t('available')}` : `❌ ${t('missing')}`}</p>
              <p>{t('vimshottari_dasha_available')}: {enhancedChart.vimshottariDasha ? `✅ ${t('available')}` : `❌ ${t('missing')}`}</p>
              <p>{t('ashtakavarga_available')}: {enhancedChart.ashtakavarga ? `✅ ${t('available')}` : `❌ ${t('missing')}`}</p>
            </div>
            </div>
          </div>
        );
      }

      return (
        <AstrologicalTables
          karakTable={enhancedChart.karakTable}
          avasthaTable={enhancedChart.avasthaTable}
          planetaryDetails={enhancedChart.planetaryDetails}
          vimshottariDasha={enhancedChart.vimshottariDasha}
          ashtakavarga={enhancedChart.ashtakavarga}
        />
      );
    } catch (error) {
      console.error('Error rendering tables:', error);
      return (
        <div className="text-center py-12">
          <div className="bg-red-800/20 rounded-lg p-6 border border-red-500/20">
            <AlertCircle className="mx-auto mb-4 text-red-400" size={48} />
            <h3 className="text-lg font-semibold text-white mb-2">{t('error_loading_tables')}</h3>
            <p className="text-red-200">{t('error_loading_tables_message')}</p>
          </div>
        </div>
      );
    }
  };

  const renderReadings = () => {
    try {
      const { readingsEn, readingsSi, generalReading, strengthsWeaknesses, careerGuidance, relationshipGuidance, healthGuidance } = birthChart;

      // Check if we have translated readings
      const hasTranslatedReadings = readingsEn || readingsSi;

      // Check if we have any reading content
      const hasReadings = generalReading || strengthsWeaknesses || careerGuidance || relationshipGuidance || healthGuidance;

      if (!hasReadings && !hasTranslatedReadings) {
        return (
          <div className="text-center py-12">
            <div className="bg-purple-800/20 rounded-lg p-6 border border-purple-500/20">
              <h3 className="text-lg font-semibold text-white mb-2">{t('birth_chart_readings')}</h3>
              <p className="text-purple-200 mb-4">
                {t('readings_not_available')}
              </p>
              <p className="text-sm text-purple-300">
                {t('readings_generated_automatically')}
              </p>
            </div>
          </div>
        );
      }

      return (
      <div className="space-y-6">
        {/* General Reading */}
        {(generalReading || (hasTranslatedReadings && (readingsEn?.generalReading || readingsSi?.generalReading))) && (
          <div className="bg-gradient-to-br from-blue-900/20 to-purple-900/20 rounded-xl p-6">
            <h3 className="text-xl font-bold text-white mb-4 flex items-center">
              <span className="text-2xl mr-2">📖</span>
              {t('general_reading')}
            </h3>
            <div className="text-gray-300 leading-relaxed">
              <TranslatedText
                text={generalReading || readingsEn?.generalReading || ''}
                translations={{
                  en: readingsEn?.generalReading || generalReading,
                  si: readingsSi?.generalReading
                }}
                className="whitespace-pre-line"
              />
            </div>
          </div>
        )}

        {/* Strengths & Weaknesses */}
        {(strengthsWeaknesses || (hasTranslatedReadings && (readingsEn?.strengthsWeaknesses || readingsSi?.strengthsWeaknesses))) && (
          <div className="bg-gradient-to-br from-green-900/20 to-teal-900/20 rounded-xl p-6">
            <h3 className="text-xl font-bold text-white mb-4 flex items-center">
              <span className="text-2xl mr-2">💪</span>
              {t('strengths_weaknesses')}
            </h3>
            <div className="text-gray-300 leading-relaxed">
              <TranslatedText
                text={strengthsWeaknesses || readingsEn?.strengthsWeaknesses || ''}
                translations={{
                  en: readingsEn?.strengthsWeaknesses || strengthsWeaknesses,
                  si: readingsSi?.strengthsWeaknesses
                }}
                className="whitespace-pre-line"
              />
            </div>
          </div>
        )}

        {/* Career Guidance */}
        {(careerGuidance || (hasTranslatedReadings && (readingsEn?.careerGuidance || readingsSi?.careerGuidance))) && (
          <div className="bg-gradient-to-br from-orange-900/20 to-red-900/20 rounded-xl p-6">
            <h3 className="text-xl font-bold text-white mb-4 flex items-center">
              <span className="text-2xl mr-2">💼</span>
              {t('career_guidance')}
            </h3>
            <div className="text-gray-300 leading-relaxed">
              <TranslatedText
                text={careerGuidance || readingsEn?.careerGuidance || ''}
                translations={{
                  en: readingsEn?.careerGuidance || careerGuidance,
                  si: readingsSi?.careerGuidance
                }}
                className="whitespace-pre-line"
              />
            </div>
          </div>
        )}

        {/* Relationship Guidance */}
        {(relationshipGuidance || (hasTranslatedReadings && (readingsEn?.relationshipGuidance || readingsSi?.relationshipGuidance))) && (
          <div className="bg-gradient-to-br from-pink-900/20 to-rose-900/20 rounded-xl p-6">
            <h3 className="text-xl font-bold text-white mb-4 flex items-center">
              <span className="text-2xl mr-2">💕</span>
              {t('relationship_guidance')}
            </h3>
            <div className="text-gray-300 leading-relaxed">
              <TranslatedText
                text={relationshipGuidance || readingsEn?.relationshipGuidance || ''}
                translations={{
                  en: readingsEn?.relationshipGuidance || relationshipGuidance,
                  si: readingsSi?.relationshipGuidance
                }}
                className="whitespace-pre-line"
              />
            </div>
          </div>
        )}

        {/* Health Guidance */}
        {(healthGuidance || (hasTranslatedReadings && (readingsEn?.healthGuidance || readingsSi?.healthGuidance))) && (
          <div className="bg-gradient-to-br from-emerald-900/20 to-green-900/20 rounded-xl p-6">
            <h3 className="text-xl font-bold text-white mb-4 flex items-center">
              <span className="text-2xl mr-2">🌿</span>
              {t('health_guidance')}
            </h3>
            <div className="text-gray-300 leading-relaxed">
              <TranslatedText
                text={healthGuidance || readingsEn?.healthGuidance || ''}
                translations={{
                  en: readingsEn?.healthGuidance || healthGuidance,
                  si: readingsSi?.healthGuidance
                }}
                className="whitespace-pre-line"
              />
            </div>
          </div>
        )}
      </div>
    );
    } catch (error) {
      console.error('Error rendering readings:', error);
      return (
        <div className="text-center py-12">
          <div className="bg-red-800/20 rounded-lg p-6 border border-red-500/20">
            <AlertCircle className="mx-auto mb-4 text-red-400" size={48} />
            <h3 className="text-lg font-semibold text-white mb-2">{t('error_loading_readings')}</h3>
            <p className="text-red-200">{t('error_loading_readings_message')}</p>
          </div>
        </div>
      );
    }
  };

  return (
    <div className={`bg-gradient-to-br from-purple-900/40 to-blue-900/40 backdrop-blur-sm rounded-2xl border border-purple-500/20 shadow-2xl ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-purple-500/20">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold text-white flex items-center">
            ⭐ {t('your_birth_chart_awaits')}
          </h2>
        </div>

        {/* Birth Details */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-4 mb-4">
          <div className="bg-purple-800/20 rounded-lg p-3 md:p-4 border border-purple-500/20">
            <div className="text-xs md:text-sm text-purple-300 mb-1">{t('ascendant')} ({t('rising_sign')})</div>
            <div className="text-base md:text-lg font-semibold text-white">{birthChart.ascendant}</div>
          </div>
          <div className="bg-purple-800/20 rounded-lg p-3 md:p-4 border border-purple-500/20">
            <div className="text-xs md:text-sm text-purple-300 mb-1">{t('moon_sign')}</div>
            <div className="text-base md:text-lg font-semibold text-white">{birthChart.moonSign}</div>
          </div>
          <div className="bg-purple-800/20 rounded-lg p-3 md:p-4 border border-purple-500/20 sm:col-span-2 lg:col-span-1">
            <div className="text-xs md:text-sm text-purple-300 mb-1">{t('sun_sign')}</div>
            <div className="text-base md:text-lg font-semibold text-white">{birthChart.sunSign}</div>
          </div>
        </div>

        {/* Birth Info */}
        {birthChart.user && (
          <div className="flex flex-wrap items-center gap-4 text-sm text-gray-300">
            {birthChart.user.birthDate && (
              <div className="flex items-center">
                <Calendar size={16} className="mr-1" />
                {new Date(birthChart.user.birthDate).toLocaleDateString()}
              </div>
            )}
            {birthChart.user.birthTime && (
              <div className="flex items-center">
                <Clock size={16} className="mr-1" />
                {birthChart.user.birthTime}
              </div>
            )}
            {birthChart.user.birthPlace && (
              <div className="flex items-center">
                <MapPin size={16} className="mr-1" />
                {birthChart.user.birthPlace}
              </div>
            )}
          </div>
        )}

        {/* Tab Navigation */}
        <div className="flex flex-wrap gap-2 mt-4">
          <button
            onClick={() => setActiveTab('charts')}
            className={`px-3 md:px-4 py-2 rounded-lg text-xs md:text-sm font-medium transition-all ${
              activeTab === 'charts'
                ? 'bg-purple-600 text-white'
                : 'bg-purple-800/20 text-purple-200 hover:bg-purple-700/30'
            }`}
          >
            {t('charts')}
          </button>
          <button
            onClick={() => setActiveTab('tables')}
            className={`px-3 md:px-4 py-2 rounded-lg text-xs md:text-sm font-medium transition-all ${
              activeTab === 'tables'
                ? 'bg-purple-600 text-white'
                : 'bg-purple-800/20 text-purple-200 hover:bg-purple-700/30'
            }`}
          >
            {t('tables')}
          </button>
          <button
            onClick={() => setActiveTab('readings')}
            className={`px-3 md:px-4 py-2 rounded-lg text-xs md:text-sm font-medium transition-all ${
              activeTab === 'readings'
                ? 'bg-purple-600 text-white'
                : 'bg-purple-800/20 text-purple-200 hover:bg-purple-700/30'
            }`}
          >
            {t('readings')}
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="p-4 md:p-6">
        {activeTab === 'charts' && renderCharts()}
        {activeTab === 'tables' && renderTables()}
        {activeTab === 'readings' && renderReadings()}
      </div>

      {/* Planetary Positions (Collapsible) */}
      <div className="border-t border-purple-500/20">
        <button
          onClick={() => setShowPlanetDetails(!showPlanetDetails)}
          className="w-full px-6 py-4 flex items-center justify-between text-left hover:bg-purple-800/20 transition-colors"
        >
          <span className="text-white font-medium">{t('planetary_positions')}</span>
          {showPlanetDetails ? (
            <ChevronUp className="text-purple-400" size={20} />
          ) : (
            <ChevronDown className="text-purple-400" size={20} />
          )}
        </button>

        {showPlanetDetails && (
          <div className="px-6 pb-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {enhancedChart.planets.map((planet: any, index: number) => (
                <div key={index} className="bg-purple-800/20 rounded-lg p-3 border border-purple-500/20">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium text-white">{planet.name}</span>
                    {planet.retrograde && (
                      <span className="text-xs bg-red-500/20 text-red-300 px-2 py-1 rounded">
                        {t('retrograde')}
                      </span>
                    )}
                  </div>
                  <div className="text-sm text-purple-200">
                    <div>{t('sign')}: {planet.sign}</div>
                    <div>{t('house')}: {planet.house}</div>
                    <div>{t('nakshatra')}: {planet.nakshatra}</div>
                    <div>{t('longitude')}: {planet.longitude.toFixed(2)}°</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="px-6 py-4 border-t border-purple-500/20 text-center">
        <p className="text-xs text-purple-300">
          {t('calculated_using_vedic')} •
          {' '}{new Date(birthChart.calculatedAt).toLocaleDateString()}
        </p>
      </div>
    </div>
  );
}
